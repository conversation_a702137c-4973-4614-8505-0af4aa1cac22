require("dotenv").config(); // ✅ Load .env first
const { onDocumentCreated } = require("firebase-functions/firestore");
// const { initializeApp } = require("firebase-admin/app");
const {  FieldValue } = require("firebase-admin/firestore");
// const { onRequest } = require("firebase-functions/v2/https");
const express = require("express");
const cors = require("cors");
// require("dotenv").config();
const functions = require("firebase-functions");
const admin = require("firebase-admin");
// const { Storage } = require("@google-cloud/storage");
// const storage = new Storage().bucket("kissandost-9570f.firebasestorage.app"); // ✅ Replace with your actual bucket name

const { analyzeUserMessage } = require("./openaiService");//analyzeTextMessage

// initializeApp();


exports.onNewChatMessage = onDocumentCreated("chats/{chatId}/messages/{messageId}", async (event) => {
  const message = event.data.data();
  const { chatId } = event.params;

  if (message.sender !== "user" || !message.content) return;

  const response = await analyzeUserMessage(message.content, message.imageUrl)//analyzeTextMessage(message.content);

  await db.collection(`chats/${chatId}/messages`).add({
    content: response,
    sender: "system",
    timestamp: FieldValue.serverTimestamp(),
  });
});



admin.initializeApp();
// const db = getFirestore();
const db = admin.firestore();
const storage = admin.storage().bucket("kissandost-9570f.firebasestorage.app"); // Firebase storage bucket

const app = express();
app.use(cors({ origin: true }));
app.use(express.json({ limit: "10mb" })); // Support large base64 images

// POST /sendMessage
app.post("/sendMessage", async (req, res) => {
  try {
    const { chatId, sender, content, imageBase64, imageName } = req.body;

    if (!chatId || !sender || (!content && !imageBase64)) {
      return res.status(400).json({ error: "Missing required fields." });
    }

    let imageUrl = null;

    // If image is provided, upload to Firebase Storage
    if (imageBase64 && imageName) {
      const buffer = Buffer.from(imageBase64, "base64");

      const filePath = `chat_images/${chatId}/${Date.now()}_${imageName}`;
      const file = storage.file(filePath);

      await file.save(buffer, {
        metadata: {
          contentType: "image/png", // or "image/png"
        },
      });

      // ✅ Make file public
      await file.makePublic();

      // ✅ Get public URL
      imageUrl = `https://storage.googleapis.com/${storage.name}/${filePath}`;
      // const buffer = Buffer.from(imageBase64, "base64");
      // const file = storage.file(`chat_images/${chatId}/${Date.now()}_${imageName}`);

      // await file.save(buffer, {
      //   metadata: { contentType: "image/jpeg" },
      //   public: true,
      // });

      // imageUrl = `https://storage.googleapis.com/${storage.name}/${file.name}`;
    }

    const message = {
      sender,
      content: content || "",
      // imageUrl: imageUrl || imageBase64,
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    if (imageUrl) {
  message.imageUrl = imageBase64;
}
    await db.collection("chats").doc(chatId).collection("messages").add(message);

    return res.status(200).json({ success: true, message: "Message stored!" });
  } catch (error) {
    console.error("Error sending message:", error);
    return res.status(500).json({ error: "Internal Server Error" + error });
  }
});

exports.api = functions.https.onRequest(app);



// const functions = require("firebase-functions");
// const admin = require("firebase-admin");
// const express = require("express");
// const cors = require("cors");
// const { analyzeTextMessage } = require("./openaiService");
// // require("dotenv").config();

// // ✅ Initialize Firebase Admin
// admin.initializeApp();

// const db = admin.firestore();

// // ✅ Firestore Trigger: on new message
// exports.onNewChatMessage = functions.firestore
//   .document("chats/{chatId}/messages/{messageId}")
//   .onCreate(async (snap, context) => {
//     const message = snap.data();
//     const { chatId } = context.params;

//     if (message.sender !== "user" || !message.content) return;

//     const response = await analyzeTextMessage(message.content);

//     await db.collection(`chats/${chatId}/messages`).add({
//       content: response,
//       sender: "system",
//       timestamp: admin.firestore.FieldValue.serverTimestamp(),
//     });
//   });

// // ✅ Express HTTP API
// const app = express();
// app.use(cors({ origin: true }));
// app.use(express.json());

// app.post("/sendMessage", async (req, res) => {
//   try {
//     const { chatId, sender, content } = req.body;

//     if (!chatId || !sender || !content) {
//       return res.status(400).json({ error: "Missing required fields." });
//     }

//     const message = {
//       sender,
//       content,
//       timestamp: admin.firestore.FieldValue.serverTimestamp(),
//     };

//     await db.collection("chats").doc(chatId).collection("messages").add(message);

//     return res.status(200).json({ success: true, message: "Message stored!" });
//   } catch (error) {
//     console.error("Error storing message:", error);
//     return res.status(500).json({ error: "Internal Server Error" });
//   }
// });

// // ✅ Export HTTP API
// exports.api = functions.https.onRequest(app);
