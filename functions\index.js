require("dotenv").config(); // ✅ Load .env first
// const { onDocumentCreated } = require("firebase-functions/firestore");
// const { initializeApp } = require("firebase-admin/app");
// const {  FieldValue } = require("firebase-admin/firestore");
// const { onRequest } = require("firebase-functions/v2/https");
const express = require("express");
const cors = require("cors");
// require("dotenv").config();
const functions = require("firebase-functions");
const admin = require("firebase-admin");
// const { Storage } = require("@google-cloud/storage");
// const storage = new Storage().bucket("kissandost-9570f.firebasestorage.app"); // ✅ Replace with your actual bucket name

const { analyzeUserMessage } = require("./openaiService");

// initializeApp();


// This trigger is now handled directly in the API endpoint for better control
// exports.onNewChatMessage = onDocumentCreated("chats/{chatId}/messages/{messageId}", async (event) => {
//   const message = event.data.data();
//   const { chatId } = event.params;

//   if (message.sender !== "user" || (!message.content && !message.imageBase64)) return;

//   const response = await analyzeUserMessage(message.content, message.imageBase64);

//   await db.collection(`chats/${chatId}/messages`).add({
//     content: typeof response === 'object' ? JSON.stringify(response, null, 2) : response,
//     sender: "system",
//     timestamp: FieldValue.serverTimestamp(),
//   });
// });



admin.initializeApp();
// const db = getFirestore();
const db = admin.firestore();

const app = express();
app.use(cors({ origin: true }));
app.use(express.json({ limit: "10mb" })); // Support large base64 images

// POST /sendMessage
app.post("/sendMessage", async (req, res) => {
  try {
    const { chatId, sender, content, imageBase64 } = req.body;

    if (!chatId || !sender || (!content && !imageBase64)) {
      return res.status(400).json({ error: "Missing required fields." });
    }

    // Save user message to Firestore
    const userMessage = {
      sender,
      content: content || "",
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
    };

    // If image is provided, store base64 directly for OpenAI processing
    if (imageBase64) {
      userMessage.imageBase64 = imageBase64;
    }

    await db.collection("chats").doc(chatId).collection("messages").add(userMessage);

    // Get AI response directly
    if (sender === "user") {
      try {
        console.log("Processing AI response...");
        const aiResponse = await analyzeUserMessage(content, imageBase64);

        // Save AI response to Firestore
        const systemMessage = {
          sender: "system",
          content: typeof aiResponse === 'object' ? JSON.stringify(aiResponse, null, 2) : aiResponse,
          timestamp: admin.firestore.FieldValue.serverTimestamp(),
        };

        await db.collection("chats").doc(chatId).collection("messages").add(systemMessage);

        return res.status(200).json({
          success: true,
          message: "Message stored and AI response generated!",
          aiResponse: aiResponse
        });
      } catch (aiError) {
        console.error("AI processing error:", aiError);
        return res.status(200).json({
          success: true,
          message: "User message stored, but AI processing failed.",
          error: aiError.message
        });
      }
    }

    return res.status(200).json({ success: true, message: "Message stored!" });
  } catch (error) {
    console.error("Error sending message:", error);
    return res.status(500).json({ error: "Internal Server Error: " + error.message });
  }
});

exports.api = functions.https.onRequest(app);
