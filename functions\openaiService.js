const { OpenAI } = require("openai");

const openai = new OpenAI({ //process.env.OPENAI_API_KEY||
  apiKey: '********************************************************************************************************************************************************************',
});




async function analyzeUserMessage(messageText = "", imageBase64 = null) {
  try {
    let userContent = [];

    if (messageText) {
      userContent.push({
        type: "text",
        text: messageText,
      });
    }

    if (imageBase64) {
      userContent.push({
        type: "image_url",
        image_url: {
          url: `data:image/jpeg;base64,${imageBase64}`,
        },
      });
    }
    const messages = [
      {
        role: "system",
        content: [
          {
            type: "text",
            text: `
You are a smart assistant for an agriculture management system.

Your job is to analyze a user's text and/or image input, and return a structured JSON response...

You are a smart assistant for an agriculture management system.

Your job is to analyze a user's text and/or image input, and return a structured JSON response that describes what entity is mentioned and what action is intended.

Supported entity types:
- plant
- animal
- field
- garden
- machinery
- inventory

Supported actions:
- create
- update
- delete
- list
- getDetails

Respond in this JSON format only:

{
  "entity": "plant" | "animal" | "field" | "machinery" | "garden" | "inventory",
  "action": "create" | "update" | "delete" | "list" | "getDetails",
  "attributes": {
    "name": "string",
    "quantity": number,
    "location": "string",
    "notes": "string"
  }
}

Do not explain anything. Only return a valid JSON object with no extra text.

Example 1:
Input: "Add 5 cows to the north field."
Output:
{
  "entity": "animal",
  "action": "create",
  "attributes": {
    "name": "cow",
    "quantity": 5,
    "location": "north field",
    "notes": ""
  }
}

Example 2:
Input: Image of a tractor with text: "Bought new tractor for west field"
Output:
{
  "entity": "machinery",
  "action": "create",
  "attributes": {
    "name": "tractor",
    "quantity": 1,
    "location": "west field",
    "notes": "bought new"
  }
},
          `
          }
        ]
      },
      {
        role: "user",
        content: userContent,
      },
    ];



    const response = await openai.chat.completions.create({
      model: "gpt-4o",
      messages,
    });

    const result = response.choices[0].message.content;

    // Optional: Try parsing as JSON and return an object
    try {
      return JSON.parse(result);
    } catch (e) {
      console.warn("Failed to parse response as JSON:", result);
      return result;
    }

  } catch (error) {
    console.error("OpenAI error:", error);
    return "Sorry, I couldn't understand your message.";
  }
}


module.exports = { analyzeUserMessage };

